# Linux MCP 服务器

这是一个基于Model Context Protocol (MCP)的Linux系统管理服务器，提供了丰富的Linux系统管理功能。

## 功能特性

### 🖥️ 系统信息
- **get_system_info**: 获取系统基本信息（操作系统、硬件、负载等）
- **check_disk_usage**: 检查磁盘使用情况
- **check_network_status**: 检查网络接口状态和连通性

### 🔧 进程管理
- **list_processes**: 列出系统进程（按CPU使用率排序）
- **execute_command**: 安全执行系统命令（带安全限制）

### 🛠️ 服务管理
- **manage_service**: 管理systemd服务（启动、停止、重启、状态查询等）

### 📁 文件系统
- **check_file_permissions**: 检查文件或目录权限
- **find_files**: 在指定目录中查找文件

### 📋 日志监控
- **monitor_logs**: 查看系统日志文件

## 安装和设置

### 1. 环境要求
- Python 3.10 或更高版本
- Linux 操作系统
- 具有适当权限的用户账户

### 2. 安装依赖

使用 uv（推荐）：
```bash
# 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建项目环境
uv venv
source .venv/bin/activate

# 安装依赖
uv pip install -e .
```

或使用 pip：
```bash
pip install mcp psutil
```

### 3. 运行服务器

```bash
# 直接运行
python linux_server.py

# 或使用 uv
uv run linux_server.py
```

## 与Claude Desktop集成

### 1. 配置Claude Desktop

编辑Claude Desktop配置文件：
- **Linux**: `~/.config/Claude/claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

添加以下配置：

```json
{
    "mcpServers": {
        "linux-server": {
            "command": "uv",
            "args": [
                "--directory",
                "/绝对路径/到/linux-mcp",
                "run",
                "linux_server.py"
            ]
        }
    }
}
```

### 2. 重启Claude Desktop

配置完成后重启Claude Desktop，您应该能在工具栏看到MCP图标🔨。

## 使用示例

### 系统监控
```
请检查系统状态
请显示CPU使用率最高的10个进程
检查根目录的磁盘使用情况
```

### 服务管理
```
检查nginx服务状态
重启apache2服务
启用ssh服务
```

### 文件操作
```
检查/etc/passwd文件的权限
在/var/log目录中查找所有.log文件
查看系统日志的最后30行
```

### 网络诊断
```
检查网络接口状态
测试网络连通性
```

## 安全特性

### 命令执行限制
为了系统安全，以下类型的命令被禁止执行：
- 删除系统文件：`rm -rf /`, `rm -rf /*`
- 格式化操作：`mkfs`, `format`
- 系统关机：`shutdown`, `reboot`, `halt`
- 用户管理：`passwd`, `userdel`, `groupdel`
- 危险的dd操作

### 权限控制
- 大部分操作以当前用户权限执行
- 服务管理操作需要sudo权限
- 文件访问受系统权限限制

## 故障排除

### 常见问题

1. **权限不足**
   ```
   解决方案：确保用户有足够权限执行相关操作，必要时使用sudo
   ```

2. **依赖缺失**
   ```bash
   # 安装缺失的依赖
   uv pip install psutil mcp
   ```

3. **Claude Desktop无法连接**
   ```
   - 检查配置文件路径是否正确
   - 确认使用绝对路径
   - 重启Claude Desktop
   ```

### 调试模式

查看详细日志：
```bash
# 在Claude Desktop日志目录查看
tail -f ~/Library/Logs/Claude/mcp*.log
```

## 开发和扩展

### 添加新工具

1. 在`linux_server.py`中添加新的工具函数：
```python
@mcp.tool()
async def your_new_tool(param: str) -> str:
    """工具描述"""
    # 实现逻辑
    return "结果"
```

2. 重启服务器测试新功能

### 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License

## 相关链接

- [MCP官方文档](https://modelcontextprotocol.io/)
- [MCP中文文档](https://mcpcn.com/)
- [Claude Desktop下载](https://claude.ai/download)
