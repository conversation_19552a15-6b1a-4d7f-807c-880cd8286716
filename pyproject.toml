[project]
name = "linux-mcp-server"
version = "0.1.0"
description = "MCP服务器用于控制Linux系统"
authors = [
    {name = "Linux MCP Server", email = "<EMAIL>"}
]
dependencies = [
    "mcp>=1.2.0",
    "asyncio",
    "subprocess",
    "psutil>=5.9.0",
]
requires-python = ">=3.10"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]
