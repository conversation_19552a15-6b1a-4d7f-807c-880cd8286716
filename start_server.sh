#!/bin/bash

# Linux MCP 服务器启动脚本

echo "🐧 启动 Linux MCP 服务器..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
echo "Python版本: $python_version"

# 检查是否安装了uv
if command -v uv &> /dev/null; then
    echo "✅ 检测到 uv 包管理器"
    
    # 使用uv运行
    echo "🚀 使用 uv 启动服务器..."
    uv run linux_server.py
else
    echo "⚠️  未检测到 uv，使用 python 直接运行"
    
    # 检查依赖
    echo "📦 检查依赖..."
    python3 -c "import mcp, psutil" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 依赖检查通过"
    else
        echo "❌ 缺少依赖，请安装："
        echo "pip install mcp psutil"
        exit 1
    fi
    
    # 直接运行
    echo "🚀 启动服务器..."
    python3 linux_server.py
fi
