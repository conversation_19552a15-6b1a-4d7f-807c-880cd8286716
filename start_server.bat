@echo off
REM Linux MCP 服务器启动脚本 (Windows版本)

echo 🐧 启动 Linux MCP 服务器...

REM 检查Python版本
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请确保Python已安装并在PATH中
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo Python版本: %python_version%

REM 检查是否安装了uv
uv --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 检测到 uv 包管理器
    echo 🚀 使用 uv 启动服务器...
    uv run linux_server.py
) else (
    echo ⚠️  未检测到 uv，使用 python 直接运行
    
    REM 检查依赖
    echo 📦 检查依赖...
    python -c "import mcp, psutil" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 依赖检查通过
    ) else (
        echo ❌ 缺少依赖，请安装：
        echo pip install mcp psutil
        pause
        exit /b 1
    )
    
    REM 直接运行
    echo 🚀 启动服务器...
    python linux_server.py
)

pause
