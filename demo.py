#!/usr/bin/env python3
"""
Linux MCP 服务器演示脚本
展示如何直接调用MCP工具
"""

import asyncio
from linux_server import (
    get_system_info,
    list_processes,
    check_disk_usage,
    execute_command,
    check_network_status,
    check_file_permissions,
    find_files
)

async def demo():
    """演示各种MCP工具功能"""
    
    print("🚀 Linux MCP 服务器功能演示")
    print("=" * 60)
    
    # 1. 系统信息
    print("\n1️⃣ 系统信息")
    print("-" * 30)
    result = await get_system_info()
    print(result)
    
    # 2. 进程列表
    print("\n2️⃣ 进程列表 (前5个)")
    print("-" * 30)
    result = await list_processes(5)
    print(result)
    
    # 3. 磁盘使用情况
    print("\n3️⃣ 磁盘使用情况")
    print("-" * 30)
    result = await check_disk_usage()
    print(result)
    
    # 4. 网络状态
    print("\n4️⃣ 网络状态")
    print("-" * 30)
    result = await check_network_status()
    print(result[:500] + "..." if len(result) > 500 else result)
    
    # 5. 命令执行演示
    print("\n5️⃣ 命令执行演示")
    print("-" * 30)
    
    # 安全命令
    print("执行安全命令: echo 'Hello from MCP!'")
    result = await execute_command("echo 'Hello from MCP!'")
    print(result)
    
    # 危险命令拦截
    print("\n尝试执行危险命令: rm -rf /")
    result = await execute_command("rm -rf /")
    print(result)
    
    # 6. 文件权限检查
    print("\n6️⃣ 文件权限检查")
    print("-" * 30)
    
    # 检查当前脚本文件
    import os
    current_file = os.path.abspath(__file__)
    print(f"检查文件: {current_file}")
    result = await check_file_permissions(current_file)
    print(result)
    
    # 7. 文件查找
    print("\n7️⃣ 文件查找")
    print("-" * 30)
    
    # 在当前目录查找Python文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"在 {current_dir} 中查找 *.py 文件")
    result = await find_files(current_dir, "*.py", 10)
    print(result)
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n💡 提示:")
    print("- 这些工具现在可以通过Claude Desktop使用")
    print("- 在Claude中尝试问: '检查系统状态'")
    print("- 或者: '显示CPU使用率最高的进程'")
    print("- 或者: '检查网络连接状态'")

if __name__ == "__main__":
    asyncio.run(demo())
