#!/usr/bin/env python3
"""
Linux MCP 服务器安装脚本
自动安装依赖并配置环境
"""

import os
import sys
import subprocess
import json
import platform
from pathlib import Path

def run_command(command, shell=True):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=shell, 
            capture_output=True, 
            text=True,
            check=True
        )
        return True, result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return False, e.stderr.strip()

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.10或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_uv():
    """安装uv包管理器"""
    print("\n📦 检查uv包管理器...")
    
    # 检查是否已安装uv
    success, _ = run_command("uv --version")
    if success:
        print("✅ uv已安装")
        return True
    
    print("📥 安装uv包管理器...")
    
    # 根据操作系统选择安装方式
    system = platform.system().lower()
    
    if system == "windows":
        # Windows安装
        success, output = run_command(
            'powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"'
        )
    else:
        # Linux/macOS安装
        success, output = run_command(
            "curl -LsSf https://astral.sh/uv/install.sh | sh"
        )
    
    if success:
        print("✅ uv安装成功")
        return True
    else:
        print(f"❌ uv安装失败: {output}")
        return False

def install_dependencies():
    """安装项目依赖"""
    print("\n📚 安装项目依赖...")
    
    # 尝试使用uv安装
    success, _ = run_command("uv --version")
    if success:
        print("使用uv安装依赖...")
        success, output = run_command("uv pip install mcp psutil")
        if success:
            print("✅ 依赖安装成功")
            return True
    
    # 回退到pip
    print("使用pip安装依赖...")
    success, output = run_command("pip install mcp psutil")
    if success:
        print("✅ 依赖安装成功")
        return True
    else:
        print(f"❌ 依赖安装失败: {output}")
        return False

def create_claude_config():
    """创建Claude Desktop配置"""
    print("\n⚙️  配置Claude Desktop...")
    
    # 获取当前目录的绝对路径
    current_dir = os.path.abspath(".")
    
    # 根据操作系统确定配置文件路径
    system = platform.system().lower()
    
    if system == "windows":
        config_dir = Path.home() / "AppData" / "Roaming" / "Claude"
    elif system == "darwin":  # macOS
        config_dir = Path.home() / "Library" / "Application Support" / "Claude"
    else:  # Linux
        config_dir = Path.home() / ".config" / "Claude"
    
    config_file = config_dir / "claude_desktop_config.json"
    
    # 创建配置目录
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # 准备配置内容
    config = {
        "mcpServers": {
            "linux-server": {
                "command": "uv",
                "args": [
                    "--directory",
                    current_dir,
                    "run",
                    "linux_server.py"
                ]
            }
        }
    }
    
    # 如果配置文件已存在，合并配置
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
            
            if "mcpServers" not in existing_config:
                existing_config["mcpServers"] = {}
            
            existing_config["mcpServers"]["linux-server"] = config["mcpServers"]["linux-server"]
            config = existing_config
        except Exception as e:
            print(f"⚠️  读取现有配置失败，将创建新配置: {e}")
    
    # 写入配置文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Claude Desktop配置已创建: {config_file}")
        print(f"📁 服务器路径: {current_dir}")
        return True
    except Exception as e:
        print(f"❌ 配置文件创建失败: {e}")
        return False

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试导入
        import mcp
        import psutil
        print("✅ 依赖导入成功")
        
        # 测试服务器启动（不实际运行）
        print("✅ 安装测试通过")
        return True
    except ImportError as e:
        print(f"❌ 依赖导入失败: {e}")
        return False

def main():
    """主安装流程"""
    print("🚀 Linux MCP 服务器安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装uv（可选）
    install_uv()
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败，请手动安装依赖:")
        print("pip install mcp psutil")
        sys.exit(1)
    
    # 创建Claude配置
    create_claude_config()
    
    # 测试安装
    if not test_installation():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 安装完成！")
    print("\n📋 下一步:")
    print("1. 重启Claude Desktop")
    print("2. 在Claude中查看MCP工具图标🔨")
    print("3. 运行测试: python test_server.py")
    print("4. 开始使用Linux管理功能！")

if __name__ == "__main__":
    main()
