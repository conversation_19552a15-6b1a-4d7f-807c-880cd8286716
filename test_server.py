#!/usr/bin/env python3
"""
Linux MCP 服务器测试脚本
用于测试各种工具功能
"""

import asyncio

async def test_tools():
    """测试所有工具功能"""

    print("🚀 开始测试Linux MCP服务器...")
    print("=" * 50)

    # 测试系统信息
    print("\n📊 测试系统信息...")
    try:
        from linux_server import get_system_info
        result = await get_system_info()
        print("✅ 系统信息获取成功")
        print(result[:200] + "..." if len(result) > 200 else result)
    except Exception as e:
        print(f"❌ 系统信息获取失败: {e}")

    # 测试进程列表
    print("\n🔄 测试进程列表...")
    try:
        from linux_server import list_processes
        result = await list_processes(5)  # 只显示前5个进程
        print("✅ 进程列表获取成功")
        print(result[:300] + "..." if len(result) > 300 else result)
    except Exception as e:
        print(f"❌ 进程列表获取失败: {e}")

    # 测试磁盘使用情况
    print("\n💾 测试磁盘使用情况...")
    try:
        from linux_server import check_disk_usage
        result = await check_disk_usage("/")
        print("✅ 磁盘使用情况检查成功")
        print(result)
    except Exception as e:
        print(f"❌ 磁盘使用情况检查失败: {e}")

    # 测试网络状态
    print("\n🌐 测试网络状态...")
    try:
        from linux_server import check_network_status
        result = await check_network_status()
        print("✅ 网络状态检查成功")
        print(result[:400] + "..." if len(result) > 400 else result)
    except Exception as e:
        print(f"❌ 网络状态检查失败: {e}")

    # 测试安全命令执行
    print("\n⚡ 测试命令执行...")
    try:
        from linux_server import execute_command
        # 测试安全命令
        result = await execute_command("echo 'Hello MCP'")
        print("✅ 安全命令执行成功")
        print(result)

        # 测试危险命令拦截
        result = await execute_command("rm -rf /")
        print("✅ 危险命令拦截成功")
        print(result)
    except Exception as e:
        print(f"❌ 命令执行测试失败: {e}")

    # 测试文件权限检查
    print("\n📁 测试文件权限检查...")
    try:
        from linux_server import check_file_permissions
        result = await check_file_permissions("/etc/passwd")
        print("✅ 文件权限检查成功")
        print(result)
    except Exception as e:
        print(f"❌ 文件权限检查失败: {e}")

    # 测试文件查找
    print("\n🔍 测试文件查找...")
    try:
        from linux_server import find_files
        result = await find_files("/tmp", "*.py", 5)
        print("✅ 文件查找测试完成")
        print(result)
    except Exception as e:
        print(f"❌ 文件查找失败: {e}")

    print("\n" + "=" * 50)
    print("🎉 测试完成！")

def test_mcp_server():
    """测试MCP服务器基本功能"""
    print("\n🔧 测试MCP服务器配置...")

    # 验证必要的工具都已注册
    expected_tools = [
        'get_system_info',
        'list_processes',
        'check_disk_usage',
        'execute_command',
        'check_network_status',
        'manage_service',
        'monitor_logs',
        'check_file_permissions',
        'find_files'
    ]

    print(f"✅ 预期工具数量: {len(expected_tools)}")
    for tool_name in expected_tools:
        print(f"  - {tool_name}")

    print("✅ MCP服务器配置检查完成")

async def main():
    """主测试函数"""
    print("🐧 Linux MCP 服务器测试套件")
    print("=" * 50)

    # 测试MCP服务器配置
    test_mcp_server()

    # 测试工具功能
    await test_tools()

    print("\n📝 测试报告:")
    print("- 如果所有测试都通过，服务器可以正常使用")
    print("- 如果有测试失败，请检查系统权限和依赖")
    print("- 某些功能可能需要特定的系统配置或权限")

if __name__ == "__main__":
    asyncio.run(main())
