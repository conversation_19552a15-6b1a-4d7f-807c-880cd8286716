#!/usr/bin/env python3
"""
Linux MCP 服务器
提供Linux系统管理功能的MCP服务器
"""

import asyncio
import os
import shutil
from typing import Any, Dict
import psutil
from mcp.server.fastmcp import FastMCP

# 初始化FastMCP服务器
mcp = FastMCP("linux-server")

async def run_command(command: str, shell: bool = True, timeout: int = 30) -> Dict[str, Any]:
    """
    安全地执行系统命令

    Args:
        command: 要执行的命令
        shell: 是否使用shell执行
        timeout: 超时时间（秒）

    Returns:
        包含命令执行结果的字典
    """
    try:
        if shell:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        else:
            process = await asyncio.create_subprocess_exec(
                *command.split(),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

        stdout, stderr = await asyncio.wait_for(
            process.communicate(),
            timeout=timeout
        )

        return {
            "success": process.returncode == 0,
            "returncode": process.returncode,
            "stdout": stdout.decode('utf-8', errors='ignore').strip(),
            "stderr": stderr.decode('utf-8', errors='ignore').strip(),
            "command": command
        }
    except asyncio.TimeoutError:
        return {
            "success": False,
            "error": f"命令执行超时 ({timeout}秒)",
            "command": command
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "command": command
        }

@mcp.tool()
async def get_system_info() -> str:
    """获取系统基本信息"""
    try:
        import platform
        import datetime

        # 获取系统信息（跨平台兼容）
        system_name = platform.system()
        release = platform.release()
        hostname = platform.node()
        machine = platform.machine()

        cpu_count = psutil.cpu_count()
        memory = psutil.virtual_memory()

        # 根据操作系统选择磁盘路径
        if system_name == "Windows":
            disk_path = "C:\\"
        else:
            disk_path = "/"

        disk = psutil.disk_usage(disk_path)

        # 获取启动时间
        boot_time = psutil.boot_time()
        boot_time_str = datetime.datetime.fromtimestamp(boot_time).strftime('%Y-%m-%d %H:%M:%S')

        info = f"""
系统信息:
========
操作系统: {system_name} {release}
主机名: {hostname}
架构: {machine}
Python版本: {platform.python_version()}

硬件信息:
========
CPU核心数: {cpu_count}
内存总量: {memory.total // (1024**3)} GB
内存使用: {memory.percent}%
磁盘总量 ({disk_path}): {disk.total // (1024**3)} GB
磁盘使用: {(disk.used / disk.total * 100):.1f}%

系统状态:
========"""

        # 添加系统负载（仅Linux/macOS）
        try:
            if hasattr(os, 'getloadavg'):
                load_avg = os.getloadavg()
                info += f"\n系统负载: {load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}"
            else:
                info += f"\n系统负载: 不支持 (Windows)"
        except:
            info += f"\n系统负载: 获取失败"

        info += f"""
启动时间: {boot_time_str}
运行时间: {(psutil.time.time() - boot_time) / 3600:.1f} 小时
"""
        return info.strip()
    except Exception as e:
        return f"获取系统信息失败: {str(e)}"

@mcp.tool()
async def list_processes(limit: int = 10) -> str:
    """
    列出系统进程

    Args:
        limit: 显示进程数量限制，默认10个
    """
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # 按CPU使用率排序
        processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)

        result = "进程列表 (按CPU使用率排序):\n"
        result += "=" * 60 + "\n"
        result += f"{'PID':<8} {'进程名':<20} {'CPU%':<8} {'内存%':<8} {'状态':<10}\n"
        result += "-" * 60 + "\n"

        for proc in processes[:limit]:
            result += f"{proc['pid']:<8} {proc['name'][:19]:<20} {proc['cpu_percent'] or 0:<8.1f} {proc['memory_percent'] or 0:<8.1f} {proc['status']:<10}\n"

        return result
    except Exception as e:
        return f"获取进程列表失败: {str(e)}"

@mcp.tool()
async def check_disk_usage(path: str = "/") -> str:
    """
    检查磁盘使用情况

    Args:
        path: 要检查的路径，默认为根目录
    """
    try:
        if not os.path.exists(path):
            return f"路径不存在: {path}"

        usage = shutil.disk_usage(path)
        total = usage.total
        used = usage.used
        free = usage.free

        result = f"""
磁盘使用情况 - {path}:
==================
总容量: {total // (1024**3):.1f} GB
已使用: {used // (1024**3):.1f} GB ({used/total*100:.1f}%)
可用空间: {free // (1024**3):.1f} GB ({free/total*100:.1f}%)
"""
        return result.strip()
    except Exception as e:
        return f"检查磁盘使用情况失败: {str(e)}"

@mcp.tool()
async def execute_command(command: str, timeout: int = 30) -> str:
    """
    执行系统命令

    Args:
        command: 要执行的命令
        timeout: 超时时间（秒），默认30秒
    """
    # 安全检查 - 禁止危险命令
    dangerous_commands = [
        'rm -rf /', 'rm -rf /*', 'mkfs', 'dd if=', 'format',
        'shutdown', 'reboot', 'halt', 'poweroff',
        'passwd', 'userdel', 'groupdel'
    ]

    for dangerous in dangerous_commands:
        if dangerous in command.lower():
            return f"安全限制: 禁止执行危险命令 '{dangerous}'"

    result = await run_command(command, timeout=timeout)

    if result["success"]:
        output = f"命令执行成功:\n命令: {command}\n"
        if result["stdout"]:
            output += f"输出:\n{result['stdout']}\n"
        if result["stderr"]:
            output += f"错误信息:\n{result['stderr']}\n"
        return output
    else:
        error_msg = f"命令执行失败:\n命令: {command}\n"
        if "error" in result:
            error_msg += f"错误: {result['error']}\n"
        if "stderr" in result and result["stderr"]:
            error_msg += f"错误输出: {result['stderr']}\n"
        return error_msg

@mcp.tool()
async def check_network_status() -> str:
    """检查网络状态"""
    try:
        # 检查网络接口
        interfaces = psutil.net_if_addrs()
        stats = psutil.net_if_stats()

        result = "网络接口状态:\n"
        result += "=" * 50 + "\n"

        for interface, addresses in interfaces.items():
            if interface in stats:
                stat = stats[interface]
                result += f"\n接口: {interface}\n"
                result += f"状态: {'UP' if stat.isup else 'DOWN'}\n"
                result += f"速度: {stat.speed} Mbps\n"

                for addr in addresses:
                    if addr.family.name == 'AF_INET':
                        result += f"IPv4: {addr.address}\n"
                    elif addr.family.name == 'AF_INET6':
                        result += f"IPv6: {addr.address}\n"

        # 检查网络连接
        ping_result = await run_command("ping -c 3 *******", timeout=10)
        result += f"\n网络连通性测试 (ping *******):\n"
        if ping_result["success"]:
            result += "✓ 网络连接正常\n"
        else:
            result += "✗ 网络连接异常\n"

        return result
    except Exception as e:
        return f"检查网络状态失败: {str(e)}"

@mcp.tool()
async def manage_service(service_name: str, action: str) -> str:
    """
    管理系统服务

    Args:
        service_name: 服务名称
        action: 操作类型 (status, start, stop, restart, enable, disable)
    """
    allowed_actions = ['status', 'start', 'stop', 'restart', 'enable', 'disable']

    if action not in allowed_actions:
        return f"不支持的操作: {action}. 支持的操作: {', '.join(allowed_actions)}"

    # 检查服务是否存在
    check_cmd = f"systemctl list-unit-files | grep -q '^{service_name}'"
    check_result = await run_command(check_cmd)

    if not check_result["success"]:
        return f"服务 '{service_name}' 不存在"

    # 执行服务操作
    if action in ['start', 'stop', 'restart', 'enable', 'disable']:
        cmd = f"sudo systemctl {action} {service_name}"
    else:  # status
        cmd = f"systemctl status {service_name}"

    result = await run_command(cmd)

    if result["success"]:
        return f"服务 '{service_name}' {action} 操作成功:\n{result['stdout']}"
    else:
        return f"服务 '{service_name}' {action} 操作失败:\n{result['stderr']}"

@mcp.tool()
async def monitor_logs(log_file: str = "/var/log/syslog", lines: int = 20) -> str:
    """
    查看系统日志

    Args:
        log_file: 日志文件路径，默认为系统日志
        lines: 显示行数，默认20行
    """
    if not os.path.exists(log_file):
        return f"日志文件不存在: {log_file}"

    cmd = f"tail -n {lines} {log_file}"
    result = await run_command(cmd)

    if result["success"]:
        return f"日志文件 {log_file} 最后 {lines} 行:\n{'='*50}\n{result['stdout']}"
    else:
        return f"读取日志失败: {result['stderr']}"

@mcp.tool()
async def check_file_permissions(file_path: str) -> str:
    """
    检查文件权限

    Args:
        file_path: 文件或目录路径
    """
    if not os.path.exists(file_path):
        return f"文件或目录不存在: {file_path}"

    try:
        import stat
        import datetime
        import platform

        stat_info = os.stat(file_path)

        # 获取权限信息
        mode = stat_info.st_mode
        permissions = stat.filemode(mode)

        # 获取所有者和组信息（跨平台兼容）
        if platform.system() == "Windows":
            owner = "Windows用户"
            group = "Windows组"
        else:
            try:
                import pwd
                import grp
                owner = pwd.getpwuid(stat_info.st_uid).pw_name
                group = grp.getgrgid(stat_info.st_gid).gr_name
            except (ImportError, KeyError):
                owner = str(stat_info.st_uid)
                group = str(stat_info.st_gid)

        # 获取文件大小
        size = stat_info.st_size

        # 获取修改时间
        mtime = datetime.datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S')

        result = f"""
文件权限信息 - {file_path}:
========================
权限: {permissions}
所有者: {owner}
所属组: {group}
大小: {size} 字节
最后修改: {mtime}
"""
        return result.strip()
    except Exception as e:
        return f"检查文件权限失败: {str(e)}"

@mcp.tool()
async def find_files(directory: str, pattern: str, max_results: int = 20) -> str:
    """
    查找文件

    Args:
        directory: 搜索目录
        pattern: 文件名模式
        max_results: 最大结果数，默认20个
    """
    if not os.path.exists(directory):
        return f"目录不存在: {directory}"

    try:
        import glob
        import platform

        # 构建搜索路径
        search_path = os.path.join(directory, "**", pattern)

        # 使用glob进行跨平台文件搜索
        files = glob.glob(search_path, recursive=True)

        # 只保留文件（不包括目录）
        files = [f for f in files if os.path.isfile(f)]

        # 限制结果数量
        files = files[:max_results]

        if files:
            result = f"在 {directory} 中找到的文件 (模式: {pattern}):\n{'='*50}\n"
            for file in files:
                result += f"{file}\n"
            return result.strip()
        else:
            return f"在 {directory} 中未找到匹配 '{pattern}' 的文件"

    except Exception as e:
        # 回退到命令行方式（仅Linux/macOS）
        import platform
        if platform.system() == "Windows":
            return f"文件搜索失败: {str(e)}"

        cmd = f"find {directory} -name '{pattern}' -type f | head -n {max_results}"
        result = await run_command(cmd)

        if result["success"]:
            if result["stdout"]:
                return f"在 {directory} 中找到的文件 (模式: {pattern}):\n{'='*50}\n{result['stdout']}"
            else:
                return f"在 {directory} 中未找到匹配 '{pattern}' 的文件"
        else:
            return f"搜索文件失败: {result['stderr']}"

if __name__ == "__main__":
    # 运行服务器
    mcp.run(transport='stdio')
